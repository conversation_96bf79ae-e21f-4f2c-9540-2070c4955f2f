# Footage Flow Setup Guide

This guide will help you set up the Footage Flow MVP application step by step.

## Prerequisites Checklist

Before starting, make sure you have:

- [ ] Node.js 18+ installed
- [ ] npm or yarn package manager
- [ ] Git installed
- [ ] A code editor (VS Code recommended)

## Service Accounts Setup

### 1. Neon.tech (Database)
- [ ] Create account at https://neon.tech
- [ ] Create a new project
- [ ] Copy the connection string
- [ ] Note: The provided connection string is already configured

### 2. Google OAuth2
- [ ] Go to Google Cloud Console (https://console.cloud.google.com)
- [ ] Create a new project or select existing
- [ ] Enable Google+ API
- [ ] Go to Credentials → Create Credentials → OAuth 2.0 Client IDs
- [ ] Set application type to "Web application"
- [ ] Add authorized redirect URIs:
  - `http://localhost:5000/auth/google/callback`
  - `https://your-domain.com/auth/google/callback` (for production)
- [ ] Copy Client ID and Client Secret
- [ ] Note: Credentials are already provided in the setup

### 3. Cloudinary (Video Storage)
- [ ] Create account at https://cloudinary.com
- [ ] Go to Dashboard
- [ ] Copy Cloud Name, API Key, and API Secret
- [ ] Create an upload preset:
  - Go to Settings → Upload
  - Click "Add upload preset"
  - Name: `footage_flow`
  - Signing Mode: "Unsigned"
  - Resource Type: "Auto"
  - Save
- [ ] Note: Credentials are already provided in the setup

### 4. OpenAI (AI Services)
- [ ] Create account at https://platform.openai.com
- [ ] Go to API Keys
- [ ] Create a new secret key
- [ ] Copy the API key
- [ ] Note: You need to add your own API key

### 5. Google Cloud Vision (Optional)
- [ ] Go to Google Cloud Console
- [ ] Enable Cloud Vision API
- [ ] Create a service account
- [ ] Download the JSON key file
- [ ] Set GOOGLE_APPLICATION_CREDENTIALS environment variable

## Installation Steps

### 1. Clone and Setup Backend

```bash
# Navigate to backend directory
cd backend

# Install dependencies
npm install

# Copy environment variables
cp .env.example .env

# Edit .env file with your credentials
# The database URL and OAuth credentials are already set
# You need to add:
# - OPENAI_API_KEY=your-openai-api-key
# - JWT_SECRET=your-random-secret-key
# - SESSION_SECRET=your-session-secret-key

# Set up database
npx prisma db push
npx prisma generate

# Start development server
npm run dev
```

### 2. Setup Frontend

```bash
# Open new terminal and navigate to frontend
cd frontend

# Install dependencies
npm install

# Copy environment variables
cp .env.example .env

# Edit .env file (Cloudinary settings are already configured)

# Start development server
npm run dev
```

### 3. Test the Application

1. Backend should be running on http://localhost:5000
2. Frontend should be running on http://localhost:5173
3. Visit http://localhost:5173 in your browser
4. Click "Continue with Google" to test authentication
5. Try uploading a video to test the full flow

## Environment Variables Reference

### Backend (.env)
```env
# Database (Already configured)
DATABASE_URL="postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require"

# Google OAuth (Already configured)
GOOGLE_CLIENT_ID="868268917843-thq0abbhemfb1goi119pi3507gk0hftp.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="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"

# Cloudinary (Already configured)
CLOUDINARY_CLOUD_NAME="do4n6dmls"
CLOUDINARY_API_KEY="618132839939623"
CLOUDINARY_API_SECRET="7T4S9KWaBOTnulm2vNc_FX1rQNE"

# You need to add these:
OPENAI_API_KEY="your-openai-api-key-here"
JWT_SECRET="your-super-secret-jwt-key-change-in-production"
SESSION_SECRET="your-session-secret-key-change-in-production"

# Server settings
PORT=5000
NODE_ENV=development
FRONTEND_URL="http://localhost:5173"
```

### Frontend (.env)
```env
# Already configured
VITE_API_URL=http://localhost:5000
VITE_CLOUDINARY_CLOUD_NAME=do4n6dmls
VITE_CLOUDINARY_UPLOAD_PRESET=footage_flow
```

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Check if the DATABASE_URL is correct
   - Ensure Neon.tech database is active
   - Run `npx prisma db push` again

2. **Google OAuth Not Working**
   - Verify redirect URIs in Google Cloud Console
   - Check if Google+ API is enabled
   - Ensure FRONTEND_URL matches your development URL

3. **Video Upload Failing**
   - Check Cloudinary credentials
   - Ensure upload preset `footage_flow` exists and is unsigned
   - Check browser console for errors

4. **AI Features Not Working**
   - Verify OpenAI API key is valid
   - Check API quota and billing
   - Look at server logs for detailed errors

### Development Tips

1. **Hot Reloading**
   - Backend: Uses nodemon for automatic restarts
   - Frontend: Vite provides instant hot reloading

2. **Database Changes**
   - After modifying schema.prisma, run:
     ```bash
     npx prisma db push
     npx prisma generate
     ```

3. **API Testing**
   - Use the health endpoint: http://localhost:5000/health
   - Check API documentation at http://localhost:5000

4. **Debugging**
   - Backend logs are in the terminal
   - Frontend errors in browser console
   - Network requests in browser dev tools

## Next Steps

After successful setup:

1. **Test Core Features**
   - [ ] User authentication
   - [ ] Video upload
   - [ ] Video listing
   - [ ] Basic search
   - [ ] Story generation

2. **Customize**
   - [ ] Update branding/colors
   - [ ] Add your own content
   - [ ] Configure production URLs

3. **Deploy**
   - [ ] Set up production database
   - [ ] Deploy backend to Railway
   - [ ] Deploy frontend to Vercel
   - [ ] Update OAuth redirect URLs

## Support

If you encounter issues:

1. Check the console logs (both frontend and backend)
2. Verify all environment variables are set correctly
3. Ensure all services (Neon, Cloudinary, etc.) are active
4. Check the GitHub repository for updates

## Security Notes

- Change JWT_SECRET and SESSION_SECRET in production
- Use environment-specific OAuth credentials
- Enable HTTPS in production
- Regularly rotate API keys
- Monitor API usage and costs
