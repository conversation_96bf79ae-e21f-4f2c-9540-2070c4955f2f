import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Upload, Search, Sparkles, Video, BarChart3, Clock, Tag } from 'lucide-react';
import toast from 'react-hot-toast';

import VideoUpload from './VideoUpload';
import VideoList from './VideoList';
import SearchVideos from './SearchVideos';
import StoryGenerator from './StoryGenerator';
import { videosAPI } from '../utils/api';
import type { User } from '../utils/auth';

interface Video {
  id: string;
  title: string;
  description?: string;
  url: string;
  duration?: number;
  uploadDate: string;
  transcriptionStatus: string;
  visionStatus: string;
  tags: Array<{
    id: string;
    label: string;
    type: string;
    confidence?: number;
  }>;
}

interface DashboardProps {
  user: User;
}

type TabType = 'overview' | 'upload' | 'search' | 'stories';

const Dashboard: React.FC<DashboardProps> = ({ user }) => {
  const [activeTab, setActiveTab] = useState<TabType>('overview');
  const [videos, setVideos] = useState<Video[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedVideo, setSelectedVideo] = useState<Video | null>(null);

  useEffect(() => {
    fetchVideos();
  }, []);

  const fetchVideos = async () => {
    try {
      const response = await videosAPI.getVideos();
      setVideos(response.data.videos);
    } catch (error) {
      console.error('Error fetching videos:', error);
      toast.error('Failed to load videos');
    } finally {
      setLoading(false);
    }
  };

  const handleVideoSelect = (video: Video) => {
    setSelectedVideo(video);
  };

  const getStats = () => {
    const totalVideos = videos.length;
    const totalDuration = videos.reduce((acc, video) => acc + (video.duration || 0), 0);
    const processedVideos = videos.filter(v => v.transcriptionStatus === 'completed' && v.visionStatus === 'completed').length;
    const totalTags = videos.reduce((acc, video) => acc + video.tags.length, 0);

    return {
      totalVideos,
      totalDuration: Math.round(totalDuration / 60), // Convert to minutes
      processedVideos,
      totalTags
    };
  };

  const stats = getStats();

  const tabs = [
    { id: 'overview', label: 'Overview', icon: BarChart3 },
    { id: 'upload', label: 'Upload', icon: Upload },
    { id: 'search', label: 'Search', icon: Search },
    { id: 'stories', label: 'Stories', icon: Sparkles },
  ] as const;

  if (loading) {
    return (
      <div className="min-h-screen bg-bg-base flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center"
        >
          <div className="w-16 h-16 border-4 border-primary-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-neutral-700 font-medium">Loading your dashboard...</p>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-bg-base">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <h1 className="text-3xl font-semibold text-neutral-900 mb-2">
            Welcome back, {user.name?.split(' ')[0]}! 👋
          </h1>
          <p className="text-neutral-700">
            Manage your video footage and create amazing stories with AI
          </p>
        </motion.div>

        {/* Stats cards */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
        >
          <div className="card spotlight-effect group">
            <div className="flex items-center">
              <div className="bg-primary-500/10 p-3 rounded-lg mr-4 group-hover:bg-primary-500/20 transition-colors duration-300">
                <Video className="w-6 h-6 text-primary-500" />
              </div>
              <div>
                <p className="text-2xl font-bold text-neutral-900">{stats.totalVideos}</p>
                <p className="text-sm text-neutral-700">Total Videos</p>
              </div>
            </div>
          </div>

          <div className="card spotlight-effect group">
            <div className="flex items-center">
              <div className="bg-blue-500/10 p-3 rounded-lg mr-4 group-hover:bg-blue-500/20 transition-colors duration-300">
                <Clock className="w-6 h-6 text-blue-500" />
              </div>
              <div>
                <p className="text-2xl font-bold text-neutral-900">{stats.totalDuration}</p>
                <p className="text-sm text-neutral-700">Minutes of Content</p>
              </div>
            </div>
          </div>

          <div className="card spotlight-effect group">
            <div className="flex items-center">
              <div className="bg-green-500/10 p-3 rounded-lg mr-4 group-hover:bg-green-500/20 transition-colors duration-300">
                <BarChart3 className="w-6 h-6 text-green-500" />
              </div>
              <div>
                <p className="text-2xl font-bold text-neutral-900">{stats.processedVideos}</p>
                <p className="text-sm text-neutral-700">AI Processed</p>
              </div>
            </div>
          </div>

          <div className="card spotlight-effect group">
            <div className="flex items-center">
              <div className="bg-purple-500/10 p-3 rounded-lg mr-4 group-hover:bg-purple-500/20 transition-colors duration-300">
                <Tag className="w-6 h-6 text-purple-500" />
              </div>
              <div>
                <p className="text-2xl font-bold text-neutral-900">{stats.totalTags}</p>
                <p className="text-sm text-neutral-700">AI Tags Generated</p>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Navigation tabs */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="mb-8"
        >
          <div className="flex space-x-1 bg-neutral-100 p-1 rounded-lg">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                  activeTab === tab.id
                    ? 'bg-bg-base text-primary-500 shadow-sm'
                    : 'text-neutral-700 hover:text-neutral-900 hover:bg-neutral-200/50'
                }`}
              >
                <tab.icon className="w-5 h-5 mr-2" />
                {tab.label}
              </button>
            ))}
          </div>
        </motion.div>

        {/* Tab content */}
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            {activeTab === 'overview' && (
              <div className="space-y-8">
                {/* Recent videos */}
                <div>
                  <div className="flex items-center justify-between mb-6">
                    <h2 className="text-xl font-semibold text-neutral-900">
                      Recent Videos
                    </h2>
                    {videos.length > 0 && (
                      <button
                        onClick={() => setActiveTab('upload')}
                        className="btn-ghost text-sm"
                      >
                        Upload More
                      </button>
                    )}
                  </div>
                  
                  {videos.length > 0 ? (
                    <VideoList
                      videos={videos.slice(0, 3)}
                      onVideoUpdate={fetchVideos}
                      onVideoSelect={handleVideoSelect}
                    />
                  ) : (
                    <div className="card text-center py-12">
                      <div className="w-16 h-16 bg-primary-500/10 rounded-full flex items-center justify-center mx-auto mb-4">
                        <Upload className="w-8 h-8 text-primary-500" />
                      </div>
                      <h3 className="text-lg font-semibold text-neutral-900 mb-2">
                        Get started with your first upload
                      </h3>
                      <p className="text-neutral-700 mb-6">
                        Upload videos to analyze them with AI and create amazing stories
                      </p>
                      <button
                        onClick={() => setActiveTab('upload')}
                        className="btn-primary"
                      >
                        <Upload className="w-5 h-5 mr-2" />
                        Upload Videos
                      </button>
                    </div>
                  )}
                </div>

                {/* Quick actions */}
                {videos.length > 0 && (
                  <div>
                    <h2 className="text-xl font-semibold text-neutral-900 mb-6">
                      Quick Actions
                    </h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div
                        onClick={() => setActiveTab('search')}
                        className="card spotlight-effect group cursor-pointer hover:shadow-lg transition-all duration-300"
                      >
                        <div className="flex items-center">
                          <div className="bg-blue-500/10 p-4 rounded-lg mr-4 group-hover:bg-blue-500/20 transition-colors duration-300">
                            <Search className="w-8 h-8 text-blue-500" />
                          </div>
                          <div>
                            <h3 className="text-lg font-semibold text-neutral-900 mb-1">
                              Search Videos
                            </h3>
                            <p className="text-neutral-700 text-sm">
                              Find specific clips using natural language
                            </p>
                          </div>
                        </div>
                      </div>

                      <div
                        onClick={() => setActiveTab('stories')}
                        className="card spotlight-effect group cursor-pointer hover:shadow-lg transition-all duration-300"
                      >
                        <div className="flex items-center">
                          <div className="bg-purple-500/10 p-4 rounded-lg mr-4 group-hover:bg-purple-500/20 transition-colors duration-300">
                            <Sparkles className="w-8 h-8 text-purple-500" />
                          </div>
                          <div>
                            <h3 className="text-lg font-semibold text-neutral-900 mb-1">
                              Generate Stories
                            </h3>
                            <p className="text-neutral-700 text-sm">
                              Create compelling narratives with AI
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'upload' && (
              <VideoUpload onUploadSuccess={fetchVideos} />
            )}

            {activeTab === 'search' && (
              <SearchVideos onVideoSelect={handleVideoSelect} />
            )}

            {activeTab === 'stories' && (
              <StoryGenerator />
            )}
          </motion.div>
        </AnimatePresence>

        {/* Video detail modal */}
        <AnimatePresence>
          {selectedVideo && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
              onClick={() => setSelectedVideo(null)}
            >
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                className="bg-bg-base rounded-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
                onClick={(e) => e.stopPropagation()}
              >
                <div className="p-6">
                  <div className="flex items-center justify-between mb-6">
                    <h2 className="text-2xl font-semibold text-neutral-900">
                      {selectedVideo.title}
                    </h2>
                    <button
                      onClick={() => setSelectedVideo(null)}
                      className="p-2 hover:bg-neutral-100 rounded-lg transition-colors duration-200"
                    >
                      ✕
                    </button>
                  </div>

                  <div className="aspect-video bg-neutral-200 rounded-lg overflow-hidden mb-6">
                    <video
                      src={selectedVideo.url}
                      controls
                      className="w-full h-full"
                    />
                  </div>

                  {selectedVideo.description && (
                    <div className="mb-6">
                      <h3 className="font-semibold text-neutral-900 mb-2">Description</h3>
                      <p className="text-neutral-700">{selectedVideo.description}</p>
                    </div>
                  )}

                  {selectedVideo.tags.length > 0 && (
                    <div className="mb-6">
                      <h3 className="font-semibold text-neutral-900 mb-3">AI Tags</h3>
                      <div className="flex flex-wrap gap-2">
                        {selectedVideo.tags.map((tag) => (
                          <span
                            key={tag.id}
                            className="px-3 py-1 bg-primary-500/10 text-primary-500 text-sm rounded-full"
                          >
                            {tag.label}
                            {tag.confidence && (
                              <span className="ml-1 text-xs opacity-75">
                                ({Math.round(tag.confidence * 100)}%)
                              </span>
                            )}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default Dashboard;
