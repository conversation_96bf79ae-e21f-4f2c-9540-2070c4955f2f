import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Sparkles, Plus, Trash2, Play, Clock, FileText, Loader } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import toast from 'react-hot-toast';

import { aiAPI } from '../utils/api';

interface Story {
  id: string;
  title: string;
  prompt: string;
  generatedScript?: string;
  status: string;
  createdAt: string;
  storyVideos: Array<{
    id: string;
    order: number;
    startTime?: number;
    endTime?: number;
    video: {
      id: string;
      title: string;
      url: string;
      duration?: number;
    };
  }>;
}

const StoryGenerator: React.FC = () => {
  const [stories, setStories] = useState<Story[]>([]);
  const [isCreating, setIsCreating] = useState(false);
  const [newStoryTitle, setNewStoryTitle] = useState('');
  const [newStoryPrompt, setNewStoryPrompt] = useState('');
  const [selectedStory, setSelectedStory] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchStories();
  }, []);

  const fetchStories = async () => {
    try {
      const response = await aiAPI.getStories();
      setStories(response.data.stories);
    } catch (error) {
      console.error('Error fetching stories:', error);
      toast.error('Failed to load stories');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateStory = async () => {
    if (!newStoryTitle.trim() || !newStoryPrompt.trim()) {
      toast.error('Please provide both title and prompt');
      return;
    }

    setIsCreating(true);
    try {
      await aiAPI.generateStory({
        title: newStoryTitle,
        prompt: newStoryPrompt
      });

      setNewStoryTitle('');
      setNewStoryPrompt('');
      toast.success('Story generation started! Check back in a moment.');
      
      // Refresh stories list
      setTimeout(fetchStories, 1000);
    } catch (error) {
      console.error('Error creating story:', error);
      toast.error('Failed to create story');
    } finally {
      setIsCreating(false);
    }
  };

  const handleDeleteStory = async (storyId: string, title: string) => {
    if (!confirm(`Are you sure you want to delete "${title}"?`)) {
      return;
    }

    try {
      await aiAPI.deleteStory(storyId);
      setStories(prev => prev.filter(s => s.id !== storyId));
      toast.success('Story deleted successfully');
    } catch (error) {
      console.error('Error deleting story:', error);
      toast.error('Failed to delete story');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-100';
      case 'processing': return 'text-yellow-600 bg-yellow-100';
      case 'failed': return 'text-red-600 bg-red-100';
      default: return 'text-neutral-600 bg-neutral-100';
    }
  };

  const formatDuration = (seconds?: number) => {
    if (!seconds) return 'Unknown';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="w-8 h-8 border-2 border-primary-500 border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center"
      >
        <h2 className="text-2xl font-semibold text-neutral-900 mb-2">
          AI Story Generator
        </h2>
        <p className="text-neutral-700">
          Create compelling narratives from your video footage using AI
        </p>
      </motion.div>

      {/* Create new story */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="card spotlight-effect"
      >
        <div className="flex items-center mb-4">
          <div className="bg-primary-500/10 p-2 rounded-lg mr-3">
            <Sparkles className="w-5 h-5 text-primary-500" />
          </div>
          <h3 className="text-lg font-semibold text-neutral-900">
            Create New Story
          </h3>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-neutral-900 mb-2">
              Story Title
            </label>
            <input
              type="text"
              value={newStoryTitle}
              onChange={(e) => setNewStoryTitle(e.target.value)}
              placeholder="e.g., My Summer Adventure"
              className="input-field"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-neutral-900 mb-2">
              Story Prompt
            </label>
            <textarea
              value={newStoryPrompt}
              onChange={(e) => setNewStoryPrompt(e.target.value)}
              placeholder="Describe the story you want to create. Be specific about the mood, theme, or narrative structure you're looking for..."
              className="input-field resize-none"
              rows={4}
            />
          </div>

          {/* Prompt suggestions */}
          <div className="p-4 bg-primary-500/5 rounded-lg">
            <h4 className="font-medium text-neutral-900 mb-2">Prompt Ideas:</h4>
            <div className="flex flex-wrap gap-2">
              {[
                "Create a travel documentary about my recent trip",
                "Make an inspiring story about overcoming challenges",
                "Tell a heartwarming story about family moments",
                "Create a dramatic narrative with emotional peaks",
                "Make a fun, upbeat story about adventures with friends"
              ].map((suggestion) => (
                <button
                  key={suggestion}
                  onClick={() => setNewStoryPrompt(suggestion)}
                  className="text-sm px-3 py-1 bg-primary-500/10 text-primary-500 rounded-full hover:bg-primary-500/20 transition-colors duration-200"
                >
                  {suggestion}
                </button>
              ))}
            </div>
          </div>

          <button
            onClick={handleCreateStory}
            disabled={isCreating || !newStoryTitle.trim() || !newStoryPrompt.trim()}
            className="btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isCreating ? (
              <>
                <Loader className="w-5 h-5 mr-2 animate-spin" />
                Generating Story...
              </>
            ) : (
              <>
                <Plus className="w-5 h-5 mr-2" />
                Generate Story
              </>
            )}
          </button>
        </div>
      </motion.div>

      {/* Stories list */}
      {stories.length > 0 ? (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-neutral-900">
            Your Stories ({stories.length})
          </h3>

          {stories.map((story, index) => (
            <motion.div
              key={story.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 + index * 0.1 }}
              className="card spotlight-effect group"
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <h4 className="text-lg font-semibold text-neutral-900 mb-1">
                    {story.title}
                  </h4>
                  <div className="flex items-center space-x-4 text-sm text-neutral-700 mb-2">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(story.status)}`}>
                      {story.status}
                    </span>
                    <span>{formatDistanceToNow(new Date(story.createdAt), { addSuffix: true })}</span>
                  </div>
                </div>

                <button
                  onClick={() => handleDeleteStory(story.id, story.title)}
                  className="p-2 text-neutral-500 hover:text-red-500 hover:bg-red-50 rounded-lg transition-colors duration-200"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>

              {/* Story prompt */}
              <div className="mb-4 p-3 bg-neutral-100/50 rounded-lg">
                <p className="text-sm text-neutral-700 italic">
                  "{story.prompt}"
                </p>
              </div>

              {/* Generated script */}
              {story.generatedScript && (
                <div className="mb-4">
                  <button
                    onClick={() => setSelectedStory(selectedStory === story.id ? null : story.id)}
                    className="flex items-center text-sm font-medium text-primary-500 hover:text-primary-400 transition-colors duration-200"
                  >
                    <FileText className="w-4 h-4 mr-2" />
                    {selectedStory === story.id ? 'Hide' : 'View'} Generated Script
                  </button>

                  <AnimatePresence>
                    {selectedStory === story.id && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        className="mt-3 p-4 bg-neutral-100/50 rounded-lg"
                      >
                        <div className="prose prose-sm max-w-none">
                          <pre className="whitespace-pre-wrap text-sm text-neutral-700 font-sans">
                            {story.generatedScript}
                          </pre>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              )}

              {/* Story videos */}
              {story.storyVideos.length > 0 && (
                <div>
                  <h5 className="font-medium text-neutral-900 mb-3">
                    Videos in Story ({story.storyVideos.length})
                  </h5>
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                    {story.storyVideos.map((storyVideo) => (
                      <div
                        key={storyVideo.id}
                        className="bg-neutral-100/50 rounded-lg p-3 group hover:bg-neutral-100 transition-colors duration-200"
                      >
                        <div className="flex items-center space-x-3">
                          <div className="w-12 h-8 bg-neutral-200 rounded overflow-hidden flex-shrink-0">
                            <video
                              src={storyVideo.video.url}
                              className="w-full h-full object-cover"
                              preload="metadata"
                            />
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-neutral-900 truncate">
                              {storyVideo.order}. {storyVideo.video.title}
                            </p>
                            <div className="flex items-center space-x-2 text-xs text-neutral-700">
                              <Clock className="w-3 h-3" />
                              <span>{formatDuration(storyVideo.video.duration)}</span>
                              {storyVideo.startTime !== undefined && storyVideo.endTime !== undefined && (
                                <span>
                                  ({Math.floor(storyVideo.startTime)}s - {Math.floor(storyVideo.endTime)}s)
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Story actions */}
              {story.status === 'completed' && story.storyVideos.length > 0 && (
                <div className="mt-4 pt-4 border-t border-neutral-300/60">
                  <button
                    onClick={() => toast.success('Story playback coming soon!')}
                    className="btn-primary text-sm px-4 py-2"
                  >
                    <Play className="w-4 h-4 mr-2" />
                    Preview Story
                  </button>
                </div>
              )}
            </motion.div>
          ))}
        </div>
      ) : (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="card text-center py-12"
        >
          <div className="w-16 h-16 bg-primary-500/10 rounded-full flex items-center justify-center mx-auto mb-4">
            <Sparkles className="w-8 h-8 text-primary-500" />
          </div>
          <h3 className="text-lg font-semibold text-neutral-900 mb-2">
            No stories yet
          </h3>
          <p className="text-neutral-700">
            Create your first AI-generated story from your video footage
          </p>
        </motion.div>
      )}
    </div>
  );
};

export default StoryGenerator;
