import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Search, Sparkles, X, Filter, Clock, Tag } from 'lucide-react';
import toast from 'react-hot-toast';

import { videosAPI, aiAPI } from '../utils/api';

interface Video {
  id: string;
  title: string;
  description?: string;
  url: string;
  duration?: number;
  uploadDate: string;
  transcription?: string;
  tags: Array<{
    id: string;
    label: string;
    type: string;
    confidence?: number;
  }>;
}

interface SearchResult {
  video: Video;
  score: number;
  matchReasons: string[];
}

interface SearchVideosProps {
  onVideoSelect?: (video: Video) => void;
}

const SearchVideos: React.FC<SearchVideosProps> = ({ onVideoSelect }) => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<Video[] | SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [searchType, setSearchType] = useState<'basic' | 'smart'>('basic');
  const [showFilters, setShowFilters] = useState(false);

  const handleBasicSearch = async () => {
    if (!query.trim()) {
      toast.error('Please enter a search query');
      return;
    }

    setIsSearching(true);
    try {
      const response = await videosAPI.searchVideos(query);
      setResults(response.data.videos);
    } catch (error) {
      console.error('Search error:', error);
      toast.error('Search failed. Please try again.');
    } finally {
      setIsSearching(false);
    }
  };

  const handleSmartSearch = async () => {
    if (!query.trim()) {
      toast.error('Please enter a search query');
      return;
    }

    setIsSearching(true);
    try {
      const response = await aiAPI.smartSearch(query);
      setResults(response.data.results);
    } catch (error) {
      console.error('Smart search error:', error);
      toast.error('Smart search failed. Please try again.');
    } finally {
      setIsSearching(false);
    }
  };

  const handleSearch = () => {
    if (searchType === 'smart') {
      handleSmartSearch();
    } else {
      handleBasicSearch();
    }
  };

  const clearSearch = () => {
    setQuery('');
    setResults([]);
  };

  const formatDuration = (seconds?: number) => {
    if (!seconds) return 'Unknown';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const isSmartResult = (result: any): result is SearchResult => {
    return 'score' in result && 'matchReasons' in result;
  };

  return (
    <div className="space-y-6">
      {/* Search header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="card"
      >
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-neutral-900">
            Search Your Videos
          </h2>
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="btn-ghost text-sm px-4 py-2"
          >
            <Filter className="w-4 h-4 mr-2" />
            Filters
          </button>
        </div>

        {/* Search type toggle */}
        <AnimatePresence>
          {showFilters && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="mb-4 p-4 bg-neutral-100/50 rounded-lg"
            >
              <h4 className="font-medium text-neutral-900 mb-3">Search Type</h4>
              <div className="flex space-x-4">
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="searchType"
                    value="basic"
                    checked={searchType === 'basic'}
                    onChange={(e) => setSearchType(e.target.value as 'basic' | 'smart')}
                    className="mr-2"
                  />
                  <span className="text-sm text-neutral-700">
                    Basic Search (title, description, tags)
                  </span>
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="searchType"
                    value="smart"
                    checked={searchType === 'smart'}
                    onChange={(e) => setSearchType(e.target.value as 'basic' | 'smart')}
                    className="mr-2"
                  />
                  <span className="text-sm text-neutral-700 flex items-center">
                    <Sparkles className="w-4 h-4 mr-1 text-primary-500" />
                    AI-Powered Search
                  </span>
                </label>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Search input */}
        <div className="flex space-x-3">
          <div className="flex-1 relative">
            <input
              type="text"
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              placeholder={
                searchType === 'smart' 
                  ? "Describe what you're looking for (e.g., 'videos with people talking outdoors')"
                  : "Search by title, description, or tags..."
              }
              className="input-field pr-10"
            />
            {query && (
              <button
                onClick={clearSearch}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-500 hover:text-neutral-700"
              >
                <X className="w-4 h-4" />
              </button>
            )}
          </div>
          
          <button
            onClick={handleSearch}
            disabled={isSearching || !query.trim()}
            className="btn-primary px-6 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSearching ? (
              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
            ) : (
              <>
                {searchType === 'smart' ? (
                  <Sparkles className="w-5 h-5 mr-2" />
                ) : (
                  <Search className="w-5 h-5 mr-2" />
                )}
                Search
              </>
            )}
          </button>
        </div>

        {/* Search suggestions */}
        {searchType === 'smart' && !query && (
          <div className="mt-4 p-4 bg-primary-500/5 rounded-lg">
            <h4 className="font-medium text-neutral-900 mb-2">Try asking:</h4>
            <div className="flex flex-wrap gap-2">
              {[
                "Videos with people speaking",
                "Outdoor scenes with nature",
                "Videos containing text or signs",
                "Clips with music or audio",
                "Recent uploads from this week"
              ].map((suggestion) => (
                <button
                  key={suggestion}
                  onClick={() => setQuery(suggestion)}
                  className="text-sm px-3 py-1 bg-primary-500/10 text-primary-500 rounded-full hover:bg-primary-500/20 transition-colors duration-200"
                >
                  {suggestion}
                </button>
              ))}
            </div>
          </div>
        )}
      </motion.div>

      {/* Search results */}
      <AnimatePresence>
        {results.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-4"
          >
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-neutral-900">
                Search Results ({results.length})
              </h3>
              {searchType === 'smart' && (
                <span className="text-sm text-neutral-700 flex items-center">
                  <Sparkles className="w-4 h-4 mr-1 text-primary-500" />
                  AI-powered results
                </span>
              )}
            </div>

            {results.map((result, index) => {
              const video = isSmartResult(result) ? result.video : result;
              const matchReasons = isSmartResult(result) ? result.matchReasons : [];
              const score = isSmartResult(result) ? result.score : 0;

              return (
                <motion.div
                  key={video.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="card spotlight-effect group hover:shadow-lg transition-all duration-300 cursor-pointer"
                  onClick={() => onVideoSelect?.(video)}
                >
                  <div className="flex items-start space-x-4">
                    {/* Video thumbnail */}
                    <div className="flex-shrink-0">
                      <div className="w-32 h-20 bg-neutral-200 rounded-lg overflow-hidden relative group-hover:scale-105 transition-transform duration-300">
                        <video
                          src={video.url}
                          className="w-full h-full object-cover"
                          preload="metadata"
                        />
                        <div className="absolute inset-0 bg-black/20 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                          <div className="bg-white/90 p-2 rounded-full">
                            <Search className="w-4 h-4 text-neutral-900" />
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Video info */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between">
                        <h4 className="text-lg font-semibold text-neutral-900 mb-1 truncate">
                          {video.title}
                        </h4>
                        {searchType === 'smart' && score > 0 && (
                          <span className="text-sm text-primary-500 font-medium ml-2">
                            {score}% match
                          </span>
                        )}
                      </div>
                      
                      {video.description && (
                        <p className="text-neutral-700 text-sm mb-2 line-clamp-2">
                          {video.description}
                        </p>
                      )}

                      <div className="flex items-center space-x-4 text-sm text-neutral-700 mb-2">
                        <div className="flex items-center space-x-1">
                          <Clock className="w-4 h-4" />
                          <span>{formatDuration(video.duration)}</span>
                        </div>
                      </div>

                      {/* Match reasons for smart search */}
                      {matchReasons.length > 0 && (
                        <div className="mb-2">
                          <p className="text-xs text-neutral-700 mb-1">Matched because:</p>
                          <div className="flex flex-wrap gap-1">
                            {matchReasons.map((reason, i) => (
                              <span
                                key={i}
                                className="text-xs px-2 py-1 bg-green-100 text-green-700 rounded-full"
                              >
                                {reason}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Tags */}
                      {video.tags.length > 0 && (
                        <div className="flex items-center space-x-2">
                          <Tag className="w-4 h-4 text-neutral-700" />
                          <div className="flex flex-wrap gap-1">
                            {video.tags.slice(0, 3).map((tag) => (
                              <span
                                key={tag.id}
                                className="px-2 py-1 bg-primary-500/10 text-primary-500 text-xs rounded-full"
                              >
                                {tag.label}
                              </span>
                            ))}
                            {video.tags.length > 3 && (
                              <span className="px-2 py-1 bg-neutral-200 text-neutral-700 text-xs rounded-full">
                                +{video.tags.length - 3} more
                              </span>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </motion.div>
        )}
      </AnimatePresence>

      {/* No results */}
      {results.length === 0 && query && !isSearching && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="card text-center py-12"
        >
          <div className="w-16 h-16 bg-neutral-200 rounded-full flex items-center justify-center mx-auto mb-4">
            <Search className="w-8 h-8 text-neutral-500" />
          </div>
          <h3 className="text-lg font-semibold text-neutral-900 mb-2">
            No videos found
          </h3>
          <p className="text-neutral-700 mb-4">
            Try adjusting your search terms or using different keywords
          </p>
          {searchType === 'basic' && (
            <button
              onClick={() => setSearchType('smart')}
              className="btn-ghost text-sm"
            >
              <Sparkles className="w-4 h-4 mr-2" />
              Try AI-powered search instead
            </button>
          )}
        </motion.div>
      )}
    </div>
  );
};

export default SearchVideos;
