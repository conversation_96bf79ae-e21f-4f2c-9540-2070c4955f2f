import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Play, Upload, Search, Sparkles, Video, Brain } from 'lucide-react';
import { getGoogleAuthUrl } from '../utils/auth';

const Login: React.FC = () => {
  const [isHovering, setIsHovering] = useState(false);

  const handleGoogleLogin = () => {
    window.location.href = getGoogleAuthUrl();
  };

  const features = [
    {
      icon: Upload,
      title: "Smart Upload",
      description: "Drag & drop videos with instant cloud storage"
    },
    {
      icon: Brain,
      title: "AI Analysis",
      description: "Automatic transcription and visual recognition"
    },
    {
      icon: Search,
      title: "Natural Search",
      description: "Find clips using everyday language"
    },
    {
      icon: Sparkles,
      title: "Story Generation",
      description: "Create compelling narratives from your footage"
    }
  ];

  return (
    <div className="min-h-screen bg-bg-base relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0 overflow-hidden">
        <svg className="absolute top-20 left-10 w-32 h-32 text-neutral-300 opacity-60" viewBox="0 0 100 100">
          <path d="M10,50 Q50,10 90,50 Q50,90 10,50" stroke="currentColor" strokeWidth="1.5" fill="none" />
        </svg>
        <svg className="absolute bottom-20 right-10 w-40 h-40 text-neutral-300 opacity-60" viewBox="0 0 100 100">
          <circle cx="50" cy="50" r="40" stroke="currentColor" strokeWidth="1.5" fill="none" />
          <circle cx="50" cy="50" r="20" stroke="currentColor" strokeWidth="1.5" fill="none" />
        </svg>
      </div>

      <div className="relative z-10 flex flex-col lg:flex-row min-h-screen">
        {/* Left side - Hero content */}
        <div className="flex-1 flex items-center justify-center px-6 py-12 lg:py-20">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="max-w-lg text-center lg:text-left"
          >
            {/* Logo */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2, duration: 0.5 }}
              className="flex items-center justify-center lg:justify-start mb-8"
            >
              <div className="bg-primary-500 p-3 rounded-xl mr-3">
                <Video className="w-8 h-8 text-white" />
              </div>
              <h1 className="text-2xl font-bold text-neutral-900">
                Footage Flow
              </h1>
            </motion.div>

            {/* Hero text */}
            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3, duration: 0.6 }}
              className="text-4xl lg:text-5xl font-semibold text-neutral-900 mb-6 leading-tight"
            >
              Transform Your
              <span className="text-gradient block">Video Stories</span>
            </motion.h2>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4, duration: 0.6 }}
              className="text-lg text-neutral-700 mb-8 leading-relaxed"
            >
              Upload, analyze, and create compelling narratives from your footage with AI-powered transcription, visual recognition, and intelligent story generation.
            </motion.p>

            {/* CTA Button */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5, duration: 0.6 }}
              className="mb-12"
            >
              <button
                onClick={handleGoogleLogin}
                onMouseEnter={() => setIsHovering(true)}
                onMouseLeave={() => setIsHovering(false)}
                className="btn-primary text-lg px-8 py-4 spotlight-effect relative group"
              >
                <span className="flex items-center">
                  <svg className="w-5 h-5 mr-3" viewBox="0 0 24 24">
                    <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                    <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                    <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                    <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                  </svg>
                  Continue with Google
                  <Play className={`w-5 h-5 ml-2 transition-transform duration-200 ${isHovering ? 'translate-x-1' : ''}`} />
                </span>
              </button>
            </motion.div>

            {/* Trust indicators */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.6, duration: 0.6 }}
              className="text-sm text-neutral-700"
            >
              <p>✨ Free to start • 🔒 Secure authentication • ⚡ Instant setup</p>
            </motion.div>
          </motion.div>
        </div>

        {/* Right side - Features showcase */}
        <div className="flex-1 bg-neutral-100/50 px-6 py-12 lg:py-20">
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.4, duration: 0.6 }}
            className="max-w-lg mx-auto"
          >
            <h3 className="text-2xl font-semibold text-neutral-900 mb-8 text-center">
              Powerful Features
            </h3>

            <div className="space-y-6">
              {features.map((feature, index) => (
                <motion.div
                  key={feature.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.6 + index * 0.1, duration: 0.5 }}
                  className="card spotlight-effect group hover:shadow-lg transition-all duration-300"
                >
                  <div className="flex items-start space-x-4">
                    <div className="bg-primary-500/10 p-3 rounded-lg group-hover:bg-primary-500/20 transition-colors duration-300">
                      <feature.icon className="w-6 h-6 text-primary-500" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-neutral-900 mb-2">
                        {feature.title}
                      </h4>
                      <p className="text-neutral-700 text-sm leading-relaxed">
                        {feature.description}
                      </p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Demo video placeholder */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1, duration: 0.6 }}
              className="mt-12 card spotlight-effect group cursor-pointer"
            >
              <div className="aspect-video bg-gradient-to-br from-primary-500/10 to-primary-400/10 rounded-lg flex items-center justify-center group-hover:from-primary-500/20 group-hover:to-primary-400/20 transition-all duration-300">
                <div className="text-center">
                  <div className="bg-primary-500 p-4 rounded-full mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                    <Play className="w-8 h-8 text-white" />
                  </div>
                  <p className="text-neutral-700 font-medium">
                    Watch Demo
                  </p>
                  <p className="text-neutral-700 text-sm">
                    See Footage Flow in action
                  </p>
                </div>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default Login;
