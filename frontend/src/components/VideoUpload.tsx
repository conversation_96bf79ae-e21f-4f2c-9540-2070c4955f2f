import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useDropzone } from 'react-dropzone';
import { Upload, X, CheckCircle, AlertCircle, Film } from 'lucide-react';
import toast from 'react-hot-toast';

import { videosAPI } from '../utils/api';

interface VideoUploadProps {
  onUploadSuccess: () => void;
}

interface UploadProgress {
  file: File;
  progress: number;
  status: 'uploading' | 'processing' | 'completed' | 'error';
  cloudinaryUrl?: string;
  publicId?: string;
  error?: string;
}

const VideoUpload: React.FC<VideoUploadProps> = ({ onUploadSuccess }) => {
  const [uploads, setUploads] = useState<UploadProgress[]>([]);
  const [isUploading, setIsUploading] = useState(false);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const videoFiles = acceptedFiles.filter(file => file.type.startsWith('video/'));
    
    if (videoFiles.length === 0) {
      toast.error('Please select video files only');
      return;
    }

    // Add files to upload queue
    const newUploads: UploadProgress[] = videoFiles.map(file => ({
      file,
      progress: 0,
      status: 'uploading'
    }));

    setUploads(prev => [...prev, ...newUploads]);
    setIsUploading(true);

    // Start uploads
    videoFiles.forEach((file, index) => {
      uploadToCloudinary(file, uploads.length + index);
    });
  }, [uploads.length]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'video/*': ['.mp4', '.mov', '.avi', '.mkv', '.webm']
    },
    multiple: true,
    maxSize: 100 * 1024 * 1024 // 100MB limit
  });

  const uploadToCloudinary = async (file: File, index: number) => {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('upload_preset', 'footage_flow'); // You'll need to create this in Cloudinary
      formData.append('resource_type', 'video');

      const response = await fetch(
        `https://api.cloudinary.com/v1_1/do4n6dmls/video/upload`,
        {
          method: 'POST',
          body: formData,
        }
      );

      if (!response.ok) {
        throw new Error('Upload failed');
      }

      const data = await response.json();

      // Update upload progress
      setUploads(prev => prev.map((upload, i) => 
        i === index 
          ? { 
              ...upload, 
              progress: 100, 
              status: 'processing',
              cloudinaryUrl: data.secure_url,
              publicId: data.public_id
            }
          : upload
      ));

      // Save to database
      await saveVideoToDatabase(file, data, index);

    } catch (error) {
      console.error('Upload error:', error);
      setUploads(prev => prev.map((upload, i) => 
        i === index 
          ? { 
              ...upload, 
              status: 'error',
              error: 'Upload failed'
            }
          : upload
      ));
      toast.error(`Failed to upload ${file.name}`);
    }
  };

  const saveVideoToDatabase = async (file: File, cloudinaryData: any, index: number) => {
    try {
      const videoData = {
        title: file.name.replace(/\.[^/.]+$/, ''), // Remove extension
        url: cloudinaryData.secure_url,
        publicId: cloudinaryData.public_id,
        duration: cloudinaryData.duration,
        size: file.size,
        format: cloudinaryData.format
      };

      await videosAPI.createVideo(videoData);

      // Update upload status
      setUploads(prev => prev.map((upload, i) => 
        i === index 
          ? { ...upload, status: 'completed' }
          : upload
      ));

      toast.success(`${file.name} uploaded successfully!`);
      onUploadSuccess();

    } catch (error) {
      console.error('Database save error:', error);
      setUploads(prev => prev.map((upload, i) => 
        i === index 
          ? { 
              ...upload, 
              status: 'error',
              error: 'Failed to save to database'
            }
          : upload
      ));
      toast.error(`Failed to save ${file.name}`);
    }
  };

  const removeUpload = (index: number) => {
    setUploads(prev => prev.filter((_, i) => i !== index));
    
    // Check if all uploads are done
    const remainingUploads = uploads.filter((_, i) => i !== index);
    if (remainingUploads.length === 0 || remainingUploads.every(u => u.status === 'completed' || u.status === 'error')) {
      setIsUploading(false);
    }
  };

  const clearCompleted = () => {
    setUploads(prev => prev.filter(upload => upload.status !== 'completed'));
  };

  return (
    <div className="space-y-6">
      {/* Upload area */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="card spotlight-effect"
      >
        <div
          {...getRootProps()}
          className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-all duration-300 ${
            isDragActive 
              ? 'border-primary-500 bg-primary-500/5' 
              : 'border-neutral-300 hover:border-primary-400 hover:bg-primary-500/2'
          }`}
        >
          <input {...getInputProps()} />
          
          <motion.div
            animate={isDragActive ? { scale: 1.05 } : { scale: 1 }}
            transition={{ duration: 0.2 }}
          >
            <div className="mx-auto w-16 h-16 bg-primary-500/10 rounded-full flex items-center justify-center mb-4">
              <Upload className="w-8 h-8 text-primary-500" />
            </div>
            
            <h3 className="text-lg font-semibold text-neutral-900 mb-2">
              {isDragActive ? 'Drop videos here' : 'Upload your videos'}
            </h3>
            
            <p className="text-neutral-700 mb-4">
              Drag and drop video files or click to browse
            </p>
            
            <div className="text-sm text-neutral-700">
              <p>Supported formats: MP4, MOV, AVI, MKV, WebM</p>
              <p>Maximum file size: 100MB per video</p>
            </div>
          </motion.div>
        </div>
      </motion.div>

      {/* Upload progress */}
      <AnimatePresence>
        {uploads.length > 0 && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="card"
          >
            <div className="flex items-center justify-between mb-4">
              <h4 className="font-semibold text-neutral-900">
                Upload Progress ({uploads.filter(u => u.status === 'completed').length}/{uploads.length})
              </h4>
              
              {uploads.some(u => u.status === 'completed') && (
                <button
                  onClick={clearCompleted}
                  className="text-sm text-neutral-700 hover:text-primary-500 transition-colors duration-200"
                >
                  Clear completed
                </button>
              )}
            </div>

            <div className="space-y-3">
              {uploads.map((upload, index) => (
                <motion.div
                  key={`${upload.file.name}-${index}`}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  className="flex items-center space-x-3 p-3 bg-neutral-100/50 rounded-lg"
                >
                  <div className="flex-shrink-0">
                    <Film className="w-5 h-5 text-neutral-700" />
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-neutral-900 truncate">
                      {upload.file.name}
                    </p>
                    <p className="text-xs text-neutral-700">
                      {(upload.file.size / (1024 * 1024)).toFixed(1)} MB
                    </p>
                  </div>

                  <div className="flex items-center space-x-2">
                    {upload.status === 'uploading' && (
                      <div className="w-6 h-6 border-2 border-primary-500 border-t-transparent rounded-full animate-spin" />
                    )}
                    
                    {upload.status === 'processing' && (
                      <div className="w-6 h-6 border-2 border-yellow-500 border-t-transparent rounded-full animate-spin" />
                    )}
                    
                    {upload.status === 'completed' && (
                      <CheckCircle className="w-6 h-6 text-green-500" />
                    )}
                    
                    {upload.status === 'error' && (
                      <AlertCircle className="w-6 h-6 text-red-500" />
                    )}

                    <button
                      onClick={() => removeUpload(index)}
                      className="p-1 hover:bg-neutral-200 rounded transition-colors duration-200"
                    >
                      <X className="w-4 h-4 text-neutral-700" />
                    </button>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default VideoUpload;
