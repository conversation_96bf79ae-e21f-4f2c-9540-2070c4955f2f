import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Video, LogOut, User, Settings, ChevronDown } from 'lucide-react';
import toast from 'react-hot-toast';

import { clearAuthData } from '../utils/auth';
import type { User as UserType } from '../utils/auth';

interface NavbarProps {
  user: UserType;
  setUser: (user: UserType | null) => void;
}

const Navbar: React.FC<NavbarProps> = ({ user, setUser }) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  const handleLogout = () => {
    clearAuthData();
    setUser(null);
    toast.success('Logged out successfully');
  };

  return (
    <nav className="bg-bg-base border-b border-neutral-300/60 sticky top-0 z-50 backdrop-blur-sm bg-bg-base/95">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="flex items-center"
          >
            <div className="bg-primary-500 p-2 rounded-lg mr-3">
              <Video className="w-6 h-6 text-white" />
            </div>
            <h1 className="text-xl font-bold text-neutral-900">
              Footage Flow
            </h1>
          </motion.div>

          {/* User menu */}
          <div className="relative">
            <motion.button
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              onClick={() => setIsDropdownOpen(!isDropdownOpen)}
              className="flex items-center space-x-3 p-2 rounded-lg hover:bg-neutral-100 transition-colors duration-200"
            >
              <img
                src={user.avatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(user.name)}&background=FF5A5F&color=fff`}
                alt={user.name}
                className="w-8 h-8 rounded-full"
              />
              <div className="hidden sm:block text-left">
                <p className="text-sm font-medium text-neutral-900">
                  {user.name}
                </p>
                <p className="text-xs text-neutral-700">
                  {user.email}
                </p>
              </div>
              <ChevronDown 
                className={`w-4 h-4 text-neutral-700 transition-transform duration-200 ${
                  isDropdownOpen ? 'rotate-180' : ''
                }`} 
              />
            </motion.button>

            {/* Dropdown menu */}
            <AnimatePresence>
              {isDropdownOpen && (
                <motion.div
                  initial={{ opacity: 0, y: -10, scale: 0.95 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  exit={{ opacity: 0, y: -10, scale: 0.95 }}
                  transition={{ duration: 0.2 }}
                  className="absolute right-0 mt-2 w-56 bg-bg-base rounded-lg shadow-lg border border-neutral-300/60 py-2"
                >
                  {/* User info */}
                  <div className="px-4 py-3 border-b border-neutral-300/60">
                    <p className="text-sm font-medium text-neutral-900">
                      {user.name}
                    </p>
                    <p className="text-xs text-neutral-700">
                      {user.email}
                    </p>
                  </div>

                  {/* Menu items */}
                  <div className="py-2">
                    <button
                      onClick={() => {
                        setIsDropdownOpen(false);
                        // Add profile functionality later
                        toast.success('Profile settings coming soon!');
                      }}
                      className="w-full flex items-center px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-100 transition-colors duration-200"
                    >
                      <User className="w-4 h-4 mr-3" />
                      Profile
                    </button>
                    
                    <button
                      onClick={() => {
                        setIsDropdownOpen(false);
                        // Add settings functionality later
                        toast.success('Settings coming soon!');
                      }}
                      className="w-full flex items-center px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-100 transition-colors duration-200"
                    >
                      <Settings className="w-4 h-4 mr-3" />
                      Settings
                    </button>
                  </div>

                  {/* Logout */}
                  <div className="border-t border-neutral-300/60 pt-2">
                    <button
                      onClick={() => {
                        setIsDropdownOpen(false);
                        handleLogout();
                      }}
                      className="w-full flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors duration-200"
                    >
                      <LogOut className="w-4 h-4 mr-3" />
                      Sign out
                    </button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </div>

      {/* Click outside to close dropdown */}
      {isDropdownOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsDropdownOpen(false)}
        />
      )}
    </nav>
  );
};

export default Navbar;
