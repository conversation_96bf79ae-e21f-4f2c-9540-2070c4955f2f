import React, { useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { motion } from 'framer-motion';
import toast from 'react-hot-toast';

import { setAuthData } from '../utils/auth';
import { authAPI } from '../utils/api';
import type { User } from '../utils/auth';

interface AuthCallbackProps {
  setUser: (user: User | null) => void;
}

const AuthCallback: React.FC<AuthCallbackProps> = ({ setUser }) => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  useEffect(() => {
    const handleAuthCallback = async () => {
      const token = searchParams.get('token');
      const error = searchParams.get('error');

      if (error) {
        toast.error('Authentication failed. Please try again.');
        navigate('/login');
        return;
      }

      if (!token) {
        toast.error('No authentication token received.');
        navigate('/login');
        return;
      }

      try {
        // Store token temporarily
        localStorage.setItem('token', token);

        // Get user info
        const response = await authAPI.getCurrentUser();
        const userData = response.data.user;

        // Set auth data
        setAuthData(token, userData);
        setUser(userData);

        toast.success(`Welcome back, ${userData.name}!`);
        navigate('/dashboard');
      } catch (error) {
        console.error('Auth callback error:', error);
        localStorage.removeItem('token');
        toast.error('Failed to complete authentication.');
        navigate('/login');
      }
    };

    handleAuthCallback();
  }, [searchParams, navigate, setUser]);

  return (
    <div className="min-h-screen bg-bg-base flex items-center justify-center">
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        className="text-center"
      >
        <div className="w-16 h-16 border-4 border-primary-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
        <h2 className="text-xl font-semibold text-neutral-900 mb-2">
          Completing Authentication
        </h2>
        <p className="text-neutral-700">
          Please wait while we set up your account...
        </p>
      </motion.div>
    </div>
  );
};

export default AuthCallback;
