import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Play, MoreVertical, Edit2, Trash2, Eye, Clock, Tag, Brain } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import toast from 'react-hot-toast';

import { videosAPI } from '../utils/api';

interface Video {
  id: string;
  title: string;
  description?: string;
  url: string;
  duration?: number;
  uploadDate: string;
  transcriptionStatus: string;
  visionStatus: string;
  tags: Array<{
    id: string;
    label: string;
    type: string;
    confidence?: number;
  }>;
}

interface VideoListProps {
  videos: Video[];
  onVideoUpdate: () => void;
  onVideoSelect?: (video: Video) => void;
}

const VideoList: React.FC<VideoListProps> = ({ videos, onVideoUpdate, onVideoSelect }) => {
  const [selectedVideo, setSelectedVideo] = useState<string | null>(null);
  const [editingVideo, setEditingVideo] = useState<string | null>(null);
  const [editTitle, setEditTitle] = useState('');
  const [editDescription, setEditDescription] = useState('');

  const handleEdit = (video: Video) => {
    setEditingVideo(video.id);
    setEditTitle(video.title);
    setEditDescription(video.description || '');
  };

  const handleSaveEdit = async (videoId: string) => {
    try {
      await videosAPI.updateVideo(videoId, {
        title: editTitle,
        description: editDescription
      });
      
      setEditingVideo(null);
      onVideoUpdate();
      toast.success('Video updated successfully');
    } catch (error) {
      console.error('Error updating video:', error);
      toast.error('Failed to update video');
    }
  };

  const handleDelete = async (videoId: string, title: string) => {
    if (!confirm(`Are you sure you want to delete "${title}"?`)) {
      return;
    }

    try {
      await videosAPI.deleteVideo(videoId);
      onVideoUpdate();
      toast.success('Video deleted successfully');
    } catch (error) {
      console.error('Error deleting video:', error);
      toast.error('Failed to delete video');
    }
  };

  const formatDuration = (seconds?: number) => {
    if (!seconds) return 'Unknown';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-100';
      case 'processing': return 'text-yellow-600 bg-yellow-100';
      case 'failed': return 'text-red-600 bg-red-100';
      default: return 'text-neutral-600 bg-neutral-100';
    }
  };

  if (videos.length === 0) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="card text-center py-12"
      >
        <div className="w-16 h-16 bg-neutral-200 rounded-full flex items-center justify-center mx-auto mb-4">
          <Play className="w-8 h-8 text-neutral-500" />
        </div>
        <h3 className="text-lg font-semibold text-neutral-900 mb-2">
          No videos yet
        </h3>
        <p className="text-neutral-700">
          Upload your first video to get started with AI analysis
        </p>
      </motion.div>
    );
  }

  return (
    <div className="space-y-4">
      {videos.map((video, index) => (
        <motion.div
          key={video.id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.1 }}
          className="card spotlight-effect group hover:shadow-lg transition-all duration-300"
        >
          <div className="flex items-start space-x-4">
            {/* Video thumbnail */}
            <div className="flex-shrink-0">
              <div className="w-32 h-20 bg-neutral-200 rounded-lg overflow-hidden relative group-hover:scale-105 transition-transform duration-300">
                <video
                  src={video.url}
                  className="w-full h-full object-cover"
                  preload="metadata"
                />
                <div className="absolute inset-0 bg-black/20 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <button
                    onClick={() => onVideoSelect?.(video)}
                    className="bg-white/90 p-2 rounded-full hover:bg-white transition-colors duration-200"
                  >
                    <Play className="w-4 h-4 text-neutral-900" />
                  </button>
                </div>
              </div>
            </div>

            {/* Video info */}
            <div className="flex-1 min-w-0">
              {editingVideo === video.id ? (
                <div className="space-y-3">
                  <input
                    type="text"
                    value={editTitle}
                    onChange={(e) => setEditTitle(e.target.value)}
                    className="input-field text-lg font-semibold"
                    placeholder="Video title"
                  />
                  <textarea
                    value={editDescription}
                    onChange={(e) => setEditDescription(e.target.value)}
                    className="input-field resize-none"
                    rows={2}
                    placeholder="Video description (optional)"
                  />
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleSaveEdit(video.id)}
                      className="btn-primary text-sm px-4 py-2"
                    >
                      Save
                    </button>
                    <button
                      onClick={() => setEditingVideo(null)}
                      className="btn-ghost text-sm px-4 py-2"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              ) : (
                <>
                  <h3 className="text-lg font-semibold text-neutral-900 mb-1 truncate">
                    {video.title}
                  </h3>
                  
                  {video.description && (
                    <p className="text-neutral-700 text-sm mb-2 line-clamp-2">
                      {video.description}
                    </p>
                  )}

                  <div className="flex items-center space-x-4 text-sm text-neutral-700 mb-3">
                    <div className="flex items-center space-x-1">
                      <Clock className="w-4 h-4" />
                      <span>{formatDuration(video.duration)}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Eye className="w-4 h-4" />
                      <span>{formatDistanceToNow(new Date(video.uploadDate), { addSuffix: true })}</span>
                    </div>
                  </div>

                  {/* AI Processing Status */}
                  <div className="flex items-center space-x-2 mb-3">
                    <div className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(video.transcriptionStatus)}`}>
                      <Brain className="w-3 h-3 inline mr-1" />
                      Transcription: {video.transcriptionStatus}
                    </div>
                    <div className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(video.visionStatus)}`}>
                      <Eye className="w-3 h-3 inline mr-1" />
                      Vision: {video.visionStatus}
                    </div>
                  </div>

                  {/* Tags */}
                  {video.tags.length > 0 && (
                    <div className="flex items-center space-x-2 mb-2">
                      <Tag className="w-4 h-4 text-neutral-700" />
                      <div className="flex flex-wrap gap-1">
                        {video.tags.slice(0, 5).map((tag) => (
                          <span
                            key={tag.id}
                            className="px-2 py-1 bg-primary-500/10 text-primary-500 text-xs rounded-full"
                          >
                            {tag.label}
                          </span>
                        ))}
                        {video.tags.length > 5 && (
                          <span className="px-2 py-1 bg-neutral-200 text-neutral-700 text-xs rounded-full">
                            +{video.tags.length - 5} more
                          </span>
                        )}
                      </div>
                    </div>
                  )}
                </>
              )}
            </div>

            {/* Actions */}
            {editingVideo !== video.id && (
              <div className="flex-shrink-0 relative">
                <button
                  onClick={() => setSelectedVideo(selectedVideo === video.id ? null : video.id)}
                  className="p-2 hover:bg-neutral-200 rounded-lg transition-colors duration-200"
                >
                  <MoreVertical className="w-5 h-5 text-neutral-700" />
                </button>

                <AnimatePresence>
                  {selectedVideo === video.id && (
                    <motion.div
                      initial={{ opacity: 0, scale: 0.95, y: -10 }}
                      animate={{ opacity: 1, scale: 1, y: 0 }}
                      exit={{ opacity: 0, scale: 0.95, y: -10 }}
                      className="absolute right-0 top-12 bg-bg-base rounded-lg shadow-lg border border-neutral-300/60 py-2 z-10 min-w-[150px]"
                    >
                      <button
                        onClick={() => {
                          handleEdit(video);
                          setSelectedVideo(null);
                        }}
                        className="w-full flex items-center px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-100 transition-colors duration-200"
                      >
                        <Edit2 className="w-4 h-4 mr-3" />
                        Edit
                      </button>
                      
                      <button
                        onClick={() => {
                          onVideoSelect?.(video);
                          setSelectedVideo(null);
                        }}
                        className="w-full flex items-center px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-100 transition-colors duration-200"
                      >
                        <Play className="w-4 h-4 mr-3" />
                        View Details
                      </button>
                      
                      <button
                        onClick={() => {
                          handleDelete(video.id, video.title);
                          setSelectedVideo(null);
                        }}
                        className="w-full flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors duration-200"
                      >
                        <Trash2 className="w-4 h-4 mr-3" />
                        Delete
                      </button>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            )}
          </div>
        </motion.div>
      ))}

      {/* Click outside to close dropdown */}
      {selectedVideo && (
        <div
          className="fixed inset-0 z-5"
          onClick={() => setSelectedVideo(null)}
        />
      )}
    </div>
  );
};

export default VideoList;
