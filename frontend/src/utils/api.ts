import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  getCurrentUser: () => api.get('/auth/me'),
  logout: () => api.post('/auth/logout'),
};

// Videos API
export const videosAPI = {
  getVideos: () => api.get('/api/videos'),
  getVideo: (id: string) => api.get(`/api/videos/${id}`),
  createVideo: (data: any) => api.post('/api/videos', data),
  updateVideo: (id: string, data: any) => api.put(`/api/videos/${id}`, data),
  deleteVideo: (id: string) => api.delete(`/api/videos/${id}`),
  searchVideos: (query: string) => api.get(`/api/videos/search/${encodeURIComponent(query)}`),
};

// AI API
export const aiAPI = {
  generateStory: (data: { prompt: string; title: string }) => api.post('/api/ai/generate-story', data),
  getStories: () => api.get('/api/ai/stories'),
  getStory: (id: string) => api.get(`/api/ai/stories/${id}`),
  deleteStory: (id: string) => api.delete(`/api/ai/stories/${id}`),
  smartSearch: (query: string) => api.post('/api/ai/smart-search', { query }),
};

export default api;
