@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply bg-bg-base text-neutral-900 antialiased;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary-500 text-white px-6 py-3 rounded-pill font-medium transition-all duration-200 hover:bg-primary-400 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }
  
  .btn-ghost {
    @apply bg-transparent text-primary-500 px-6 py-3 rounded-pill font-medium transition-all duration-200 hover:underline focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }
  
  .card {
    @apply bg-neutral-100 rounded-xl shadow-sm border border-neutral-300/60 p-6;
  }
  
  .input-field {
    @apply w-full px-4 py-3 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200;
  }
  
  .spotlight-effect {
    position: relative;
    overflow: hidden;
  }
  
  .spotlight-effect::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at var(--mouse-x, 50%) var(--mouse-y, 50%), rgba(255, 90, 95, 0.1) 0%, transparent 50%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
  }
  
  .spotlight-effect:hover::before {
    opacity: 1;
  }
}

@layer utilities {
  .text-gradient {
    @apply bg-gradient-to-r from-primary-500 to-primary-400 bg-clip-text text-transparent;
  }
  
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }
  
  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }
}
