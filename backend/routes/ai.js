const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../middleware/auth');
const OpenAI = require('openai');

const router = express.Router();
const prisma = new PrismaClient();

// Configure OpenAI
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY
});

// Generate story from prompt
router.post('/generate-story', authenticateToken, async (req, res) => {
  try {
    const { prompt, title } = req.body;

    if (!prompt || !title) {
      return res.status(400).json({ error: 'Prompt and title are required' });
    }

    // Create story record
    const story = await prisma.story.create({
      data: {
        title,
        prompt,
        status: 'processing',
        userId: req.user.id
      }
    });

    // Start story generation in background
    generateStoryScript(story.id, prompt, req.user.id);

    res.status(201).json({ story });
  } catch (error) {
    console.error('Error creating story:', error);
    res.status(500).json({ error: 'Failed to create story' });
  }
});

// Get user's stories
router.get('/stories', authenticateToken, async (req, res) => {
  try {
    const stories = await prisma.story.findMany({
      where: { userId: req.user.id },
      include: {
        storyVideos: {
          include: {
            video: {
              select: {
                id: true,
                title: true,
                url: true,
                duration: true
              }
            }
          },
          orderBy: { order: 'asc' }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    res.json({ stories });
  } catch (error) {
    console.error('Error fetching stories:', error);
    res.status(500).json({ error: 'Failed to fetch stories' });
  }
});

// Get single story
router.get('/stories/:id', authenticateToken, async (req, res) => {
  try {
    const story = await prisma.story.findFirst({
      where: { 
        id: req.params.id,
        userId: req.user.id 
      },
      include: {
        storyVideos: {
          include: {
            video: true
          },
          orderBy: { order: 'asc' }
        }
      }
    });

    if (!story) {
      return res.status(404).json({ error: 'Story not found' });
    }

    res.json({ story });
  } catch (error) {
    console.error('Error fetching story:', error);
    res.status(500).json({ error: 'Failed to fetch story' });
  }
});

// Delete story
router.delete('/stories/:id', authenticateToken, async (req, res) => {
  try {
    const story = await prisma.story.findFirst({
      where: { 
        id: req.params.id,
        userId: req.user.id 
      }
    });

    if (!story) {
      return res.status(404).json({ error: 'Story not found' });
    }

    await prisma.story.delete({
      where: { id: req.params.id }
    });

    res.json({ message: 'Story deleted successfully' });
  } catch (error) {
    console.error('Error deleting story:', error);
    res.status(500).json({ error: 'Failed to delete story' });
  }
});

// Smart search with AI
router.post('/smart-search', authenticateToken, async (req, res) => {
  try {
    const { query } = req.body;

    if (!query) {
      return res.status(400).json({ error: 'Search query is required' });
    }

    // Get user's videos with tags and transcriptions
    const videos = await prisma.video.findMany({
      where: { userId: req.user.id },
      include: {
        tags: true
      }
    });

    // Use AI to understand the query and match with video content
    const searchResults = await performSmartSearch(query, videos);

    res.json({ results: searchResults, query });
  } catch (error) {
    console.error('Error performing smart search:', error);
    res.status(500).json({ error: 'Failed to perform smart search' });
  }
});

// Background story generation function
async function generateStoryScript(storyId, prompt, userId) {
  try {
    console.log(`Generating story script for story ${storyId}`);

    // Get user's videos to understand available content
    const videos = await prisma.video.findMany({
      where: { userId },
      include: { tags: true }
    });

    // Create context for AI about available videos
    const videoContext = videos.map(video => ({
      id: video.id,
      title: video.title,
      tags: video.tags.map(tag => tag.label),
      transcription: video.transcription
    }));

    // Generate story script with OpenAI
    const completion = await openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        {
          role: "system",
          content: `You are a creative video story generator. Based on the user's prompt and available videos, create a compelling story script that incorporates the available video content. 

Available videos: ${JSON.stringify(videoContext, null, 2)}

Create a story that:
1. Uses the available video content creatively
2. Has a clear narrative structure
3. Suggests which videos to use and in what order
4. Includes timing suggestions for video clips`
        },
        {
          role: "user",
          content: prompt
        }
      ],
      max_tokens: 1000,
      temperature: 0.7
    });

    const generatedScript = completion.choices[0].message.content;

    // Update story with generated script
    await prisma.story.update({
      where: { id: storyId },
      data: {
        generatedScript,
        status: 'completed'
      }
    });

    // For demo purposes, automatically link some videos to the story
    if (videos.length > 0) {
      const storyVideos = videos.slice(0, Math.min(3, videos.length)).map((video, index) => ({
        storyId,
        videoId: video.id,
        order: index + 1,
        startTime: 0,
        endTime: video.duration || 30
      }));

      await prisma.storyVideo.createMany({
        data: storyVideos
      });
    }

    console.log(`Story script generated successfully for story ${storyId}`);
  } catch (error) {
    console.error(`Error generating story script for story ${storyId}:`, error);
    
    await prisma.story.update({
      where: { id: storyId },
      data: { status: 'failed' }
    });
  }
}

// Smart search function using AI
async function performSmartSearch(query, videos) {
  try {
    // For demo purposes, implement a simple keyword-based search
    // In production, you would use embeddings and semantic search
    
    const queryLower = query.toLowerCase();
    const results = [];

    for (const video of videos) {
      let score = 0;
      let matchReasons = [];

      // Check title match
      if (video.title.toLowerCase().includes(queryLower)) {
        score += 10;
        matchReasons.push('Title match');
      }

      // Check transcription match
      if (video.transcription && video.transcription.toLowerCase().includes(queryLower)) {
        score += 8;
        matchReasons.push('Transcription match');
      }

      // Check tag matches
      const matchingTags = video.tags.filter(tag => 
        tag.label.toLowerCase().includes(queryLower)
      );
      if (matchingTags.length > 0) {
        score += matchingTags.length * 5;
        matchReasons.push(`Tag matches: ${matchingTags.map(t => t.label).join(', ')}`);
      }

      if (score > 0) {
        results.push({
          video,
          score,
          matchReasons
        });
      }
    }

    // Sort by score descending
    results.sort((a, b) => b.score - a.score);

    return results;
  } catch (error) {
    console.error('Error in smart search:', error);
    return [];
  }
}

module.exports = router;
