const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../middleware/auth');
const cloudinary = require('cloudinary').v2;
const OpenAI = require('openai');

const router = express.Router();
const prisma = new PrismaClient();

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET
});

// Configure OpenAI
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY
});

// Get all videos for authenticated user
router.get('/', authenticateToken, async (req, res) => {
  try {
    const videos = await prisma.video.findMany({
      where: { userId: req.user.id },
      include: {
        tags: true,
        _count: {
          select: { tags: true }
        }
      },
      orderBy: { uploadDate: 'desc' }
    });

    res.json({ videos });
  } catch (error) {
    console.error('Error fetching videos:', error);
    res.status(500).json({ error: 'Failed to fetch videos' });
  }
});

// Get single video with details
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const video = await prisma.video.findFirst({
      where: { 
        id: req.params.id,
        userId: req.user.id 
      },
      include: {
        tags: {
          orderBy: { timestamp: 'asc' }
        }
      }
    });

    if (!video) {
      return res.status(404).json({ error: 'Video not found' });
    }

    res.json({ video });
  } catch (error) {
    console.error('Error fetching video:', error);
    res.status(500).json({ error: 'Failed to fetch video' });
  }
});

// Create new video record after Cloudinary upload
router.post('/', authenticateToken, async (req, res) => {
  try {
    const { title, description, url, publicId, duration, size, format } = req.body;

    if (!title || !url || !publicId) {
      return res.status(400).json({ error: 'Title, URL, and publicId are required' });
    }

    const video = await prisma.video.create({
      data: {
        title,
        description,
        url,
        publicId,
        duration,
        size,
        format,
        userId: req.user.id
      }
    });

    // Start AI processing in background
    processVideoAI(video.id);

    res.status(201).json({ video });
  } catch (error) {
    console.error('Error creating video:', error);
    res.status(500).json({ error: 'Failed to create video record' });
  }
});

// Update video
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const { title, description } = req.body;

    const video = await prisma.video.findFirst({
      where: { 
        id: req.params.id,
        userId: req.user.id 
      }
    });

    if (!video) {
      return res.status(404).json({ error: 'Video not found' });
    }

    const updatedVideo = await prisma.video.update({
      where: { id: req.params.id },
      data: { title, description }
    });

    res.json({ video: updatedVideo });
  } catch (error) {
    console.error('Error updating video:', error);
    res.status(500).json({ error: 'Failed to update video' });
  }
});

// Delete video
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const video = await prisma.video.findFirst({
      where: { 
        id: req.params.id,
        userId: req.user.id 
      }
    });

    if (!video) {
      return res.status(404).json({ error: 'Video not found' });
    }

    // Delete from Cloudinary
    try {
      await cloudinary.uploader.destroy(video.publicId, { resource_type: 'video' });
    } catch (cloudinaryError) {
      console.error('Error deleting from Cloudinary:', cloudinaryError);
      // Continue with database deletion even if Cloudinary fails
    }

    // Delete from database (cascade will handle tags)
    await prisma.video.delete({
      where: { id: req.params.id }
    });

    res.json({ message: 'Video deleted successfully' });
  } catch (error) {
    console.error('Error deleting video:', error);
    res.status(500).json({ error: 'Failed to delete video' });
  }
});

// Search videos
router.get('/search/:query', authenticateToken, async (req, res) => {
  try {
    const query = req.params.query.toLowerCase();

    const videos = await prisma.video.findMany({
      where: {
        userId: req.user.id,
        OR: [
          { title: { contains: query, mode: 'insensitive' } },
          { description: { contains: query, mode: 'insensitive' } },
          { transcription: { contains: query, mode: 'insensitive' } },
          {
            tags: {
              some: {
                label: { contains: query, mode: 'insensitive' }
              }
            }
          }
        ]
      },
      include: {
        tags: true
      },
      orderBy: { uploadDate: 'desc' }
    });

    res.json({ videos, query });
  } catch (error) {
    console.error('Error searching videos:', error);
    res.status(500).json({ error: 'Failed to search videos' });
  }
});

// Background AI processing function
async function processVideoAI(videoId) {
  try {
    console.log(`Starting AI processing for video ${videoId}`);
    
    // Update status to processing
    await prisma.video.update({
      where: { id: videoId },
      data: { 
        transcriptionStatus: 'processing',
        visionStatus: 'processing'
      }
    });

    // Note: In a real implementation, you would:
    // 1. Extract audio from video and send to OpenAI Whisper
    // 2. Extract frames and send to Google Cloud Vision
    // 3. Store results in database
    
    // For now, we'll simulate processing with mock data
    setTimeout(async () => {
      try {
        await prisma.video.update({
          where: { id: videoId },
          data: {
            transcriptionStatus: 'completed',
            visionStatus: 'completed',
            transcription: 'Mock transcription: This is a sample video with various objects and scenes.'
          }
        });

        // Add some mock tags
        await prisma.tag.createMany({
          data: [
            { videoId, label: 'person', confidence: 0.95, type: 'vision', timestamp: 5.2 },
            { videoId, label: 'outdoor', confidence: 0.88, type: 'vision', timestamp: 12.1 },
            { videoId, label: 'speaking', confidence: 0.92, type: 'transcription', timestamp: 8.5 }
          ]
        });

        console.log(`AI processing completed for video ${videoId}`);
      } catch (error) {
        console.error(`Error completing AI processing for video ${videoId}:`, error);
        await prisma.video.update({
          where: { id: videoId },
          data: {
            transcriptionStatus: 'failed',
            visionStatus: 'failed'
          }
        });
      }
    }, 5000); // Simulate 5 second processing time

  } catch (error) {
    console.error(`Error starting AI processing for video ${videoId}:`, error);
  }
}

module.exports = router;
