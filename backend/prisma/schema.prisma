// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  googleId  String   @unique
  name      String?
  avatar    String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  videos Video[]
  stories Story[]

  @@map("users")
}

model Video {
  id          String   @id @default(cuid())
  title       String
  description String?
  url         String   // Cloudinary URL
  publicId    String   // Cloudinary public ID
  duration    Float?   // Duration in seconds
  size        Int?     // File size in bytes
  format      String?  // Video format (mp4, mov, etc.)
  uploadDate  DateTime @default(now())
  
  // AI Processing Status
  transcriptionStatus String @default("pending") // pending, processing, completed, failed
  visionStatus       String @default("pending") // pending, processing, completed, failed
  transcription      String? // Full transcription text
  
  // User relation
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Relations
  tags   Tag[]
  storyVideos StoryVideo[]

  @@map("videos")
}

model Tag {
  id        String   @id @default(cuid())
  label     String   // Tag name/description
  confidence Float?  // Confidence score from AI
  timestamp Float?   // Timestamp in video (seconds)
  type      String   // "vision" or "transcription"
  createdAt DateTime @default(now())

  // Video relation
  videoId String
  video   Video  @relation(fields: [videoId], references: [id], onDelete: Cascade)

  @@map("tags")
}

model Story {
  id               String   @id @default(cuid())
  title            String
  prompt           String   // User's story generation prompt
  generatedScript  String?  // AI-generated story script
  status           String   @default("pending") // pending, processing, completed, failed
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  // User relation
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Relations
  storyVideos StoryVideo[]

  @@map("stories")
}

model StoryVideo {
  id       String @id @default(cuid())
  order    Int    // Order in the story sequence
  startTime Float? // Start time in the video (seconds)
  endTime   Float? // End time in the video (seconds)

  // Relations
  storyId String
  story   Story  @relation(fields: [storyId], references: [id], onDelete: Cascade)
  
  videoId String
  video   Video  @relation(fields: [videoId], references: [id], onDelete: Cascade)

  @@unique([storyId, order])
  @@map("story_videos")
}
