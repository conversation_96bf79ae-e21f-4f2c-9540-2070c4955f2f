# Database
DATABASE_URL="postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require"

# Google OAuth
GOOGLE_CLIENT_ID="868268917843-thq0abbhemfb1goi119pi3507gk0hftp.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="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"

# JWT
JWT_SECRET="your-super-secret-jwt-key-change-in-production"

# Cloudinary
CLOUDINARY_CLOUD_NAME="do4n6dmls"
CLOUDINARY_API_KEY="***************"
CLOUDINARY_API_SECRET="7T4S9KWaBOTnulm2vNc_FX1rQNE"
CLOUDINARY_URL="cloudinary://***************:7T4S9KWaBOTnulm2vNc_FX1rQNE@do4n6dmls"

# OpenAI
OPENAI_API_KEY="your-openai-api-key-here"

# Google Cloud Vision
GOOGLE_CLOUD_PROJECT_ID="your-google-cloud-project-id"
GOOGLE_APPLICATION_CREDENTIALS="path-to-your-service-account-key.json"

# Server
PORT=5000
NODE_ENV=development
FRONTEND_URL="http://localhost:5173"

# Session
SESSION_SECRET="your-session-secret-key-change-in-production"
