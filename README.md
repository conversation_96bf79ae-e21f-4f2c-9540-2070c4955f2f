# Footage Flow MVP

A web-based AI-powered video management application that allows users to upload, analyze, and create stories from their video footage.

## Features

- **User Authentication**: Google OAuth2 integration
- **Video Upload**: Cloudinary integration with drag & drop interface
- **AI Analysis**: 
  - Automatic transcription using OpenAI Whisper
  - Visual recognition using Google Cloud Vision API
- **Smart Search**: Natural language video search
- **Story Generation**: AI-powered narrative creation from video clips
- **Modern UI**: React with TypeScript, Tailwind CSS, and Framer Motion

## Tech Stack

### Backend
- Node.js with Express.js
- PostgreSQL with Prisma ORM
- Google OAuth2 with JWT
- Cloudinary for video storage
- OpenAI GPT-4 and Whisper
- Google Cloud Vision API

### Frontend
- React with TypeScript
- Tailwind CSS with custom design system
- Framer Motion for animations
- Axios for API calls
- React Router for navigation

## Quick Start

### Prerequisites
- Node.js 18+ and npm
- PostgreSQL database (Neon.tech recommended)
- Google OAuth2 credentials
- Cloudinary account
- OpenAI API key
- Google Cloud Vision API credentials

### Backend Setup

1. Navigate to backend directory:
```bash
cd backend
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables in `.env`:
```env
DATABASE_URL="your-postgresql-connection-string"
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
JWT_SECRET="your-jwt-secret"
CLOUDINARY_CLOUD_NAME="your-cloudinary-cloud-name"
CLOUDINARY_API_KEY="your-cloudinary-api-key"
CLOUDINARY_API_SECRET="your-cloudinary-api-secret"
OPENAI_API_KEY="your-openai-api-key"
PORT=5000
FRONTEND_URL="http://localhost:5173"
SESSION_SECRET="your-session-secret"
```

4. Set up database:
```bash
npx prisma db push
npx prisma generate
```

5. Start the server:
```bash
npm run dev
```

### Frontend Setup

1. Navigate to frontend directory:
```bash
cd frontend
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables in `.env`:
```env
VITE_API_URL=http://localhost:5000
VITE_CLOUDINARY_CLOUD_NAME=your-cloudinary-cloud-name
VITE_CLOUDINARY_UPLOAD_PRESET=footage_flow
```

4. Start the development server:
```bash
npm run dev
```

### Cloudinary Setup

1. Create an upload preset named `footage_flow` in your Cloudinary dashboard
2. Set it to "Unsigned" mode
3. Configure it for video uploads

### Google OAuth Setup

1. Create a project in Google Cloud Console
2. Enable Google+ API
3. Create OAuth2 credentials
4. Add authorized redirect URIs:
   - `http://localhost:5000/auth/google/callback`
   - `https://your-production-domain.com/auth/google/callback`

## Project Structure

```
footage-flow/
├── backend/
│   ├── config/
│   │   └── passport.js
│   ├── middleware/
│   │   └── auth.js
│   ├── prisma/
│   │   └── schema.prisma
│   ├── routes/
│   │   ├── auth.js
│   │   ├── videos.js
│   │   └── ai.js
│   ├── .env
│   ├── package.json
│   └── server.js
├── frontend/
│   ├── src/
│   │   ├── components/
│   │   │   ├── AuthCallback.tsx
│   │   │   ├── Dashboard.tsx
│   │   │   ├── Login.tsx
│   │   │   ├── Navbar.tsx
│   │   │   ├── ProtectedRoute.tsx
│   │   │   ├── SearchVideos.tsx
│   │   │   ├── StoryGenerator.tsx
│   │   │   ├── VideoList.tsx
│   │   │   └── VideoUpload.tsx
│   │   ├── utils/
│   │   │   ├── api.ts
│   │   │   └── auth.ts
│   │   ├── App.tsx
│   │   ├── index.css
│   │   └── main.tsx
│   ├── .env
│   ├── package.json
│   ├── tailwind.config.js
│   └── vite.config.ts
└── README.md
```

## API Endpoints

### Authentication
- `GET /auth/google` - Initiate Google OAuth
- `GET /auth/google/callback` - OAuth callback
- `GET /auth/me` - Get current user
- `POST /auth/logout` - Logout

### Videos
- `GET /api/videos` - Get user's videos
- `GET /api/videos/:id` - Get single video
- `POST /api/videos` - Create video record
- `PUT /api/videos/:id` - Update video
- `DELETE /api/videos/:id` - Delete video
- `GET /api/videos/search/:query` - Search videos

### AI Features
- `POST /api/ai/generate-story` - Generate story from prompt
- `GET /api/ai/stories` - Get user's stories
- `GET /api/ai/stories/:id` - Get single story
- `DELETE /api/ai/stories/:id` - Delete story
- `POST /api/ai/smart-search` - AI-powered search

## Design System

The application uses a custom design system based on the provided brand palette:

### Colors
- Primary: `#FF5A5F` (primary-500)
- Primary Hover: `#FF7C82` (primary-400)
- Neutral: Various shades from `#0F0F11` to `#F7F7F9`
- Background: `#FFFFFF`

### Components
- Buttons with pill shape and hover effects
- Cards with subtle shadows and spotlight effects
- Consistent spacing and typography using Inter font

## Development Notes

### AI Processing
- Video transcription and vision analysis are currently mocked for development
- In production, implement actual OpenAI Whisper and Google Cloud Vision integration
- Processing status is tracked in the database

### File Upload
- Videos are uploaded directly to Cloudinary
- Metadata is stored in PostgreSQL
- Maximum file size: 100MB per video

### Authentication
- JWT tokens expire in 7 days
- Refresh token mechanism not implemented (add for production)
- User sessions are managed client-side

## Deployment

### Backend (Railway)
1. Connect your GitHub repository
2. Set environment variables
3. Deploy automatically on push

### Frontend (Vercel)
1. Connect your GitHub repository
2. Set build command: `npm run build`
3. Set environment variables
4. Deploy automatically on push

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

MIT License - see LICENSE file for details
